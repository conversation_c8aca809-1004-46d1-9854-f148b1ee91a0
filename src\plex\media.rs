//! Plex media metadata handling

use crate::discord::activity::MediaType;
use crate::error::{PlexError, Result};
use crate::plex::api::PlexApiClient;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// Plex media item
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PlexMediaItem {
    #[serde(rename = "ratingKey")]
    pub rating_key: String,
    pub key: String,
    #[serde(rename = "type")]
    pub media_type: String,
    pub title: String,
    #[serde(rename = "grandparentTitle")]
    pub grandparent_title: Option<String>,
    #[serde(rename = "parentTitle")]
    pub parent_title: Option<String>,
    #[serde(rename = "originalTitle")]
    pub original_title: Option<String>,
    pub year: Option<i32>,
    pub duration: Option<u64>, // in milliseconds
    #[serde(rename = "viewOffset")]
    pub view_offset: Option<u64>, // in milliseconds
    pub thumb: Option<String>,
    #[serde(rename = "grandparentThumb")]
    pub grandparent_thumb: Option<String>,
    #[serde(rename = "parentThumb")]
    pub parent_thumb: Option<String>,
    #[serde(rename = "parentIndex")]
    pub parent_index: Option<i32>, // Season number
    pub index: Option<i32>, // Episode number
    #[serde(rename = "parentRatingKey")]
    pub parent_rating_key: Option<String>,
    #[serde(rename = "grandparentRatingKey")]
    pub grandparent_rating_key: Option<String>,
    #[serde(default)]
    pub genres: Vec<PlexGenre>,
    #[serde(default)]
    pub guids: Vec<PlexGuid>,
}

/// Plex genre information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PlexGenre {
    pub tag: String,
}

/// Plex GUID (external identifier)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PlexGuid {
    pub id: String,
}

/// Library section information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LibrarySection {
    pub key: String,
    pub title: String,
    #[serde(rename = "type")]
    pub section_type: String,
}

/// Media metadata service
pub struct MediaMetadataService {
    api_client: PlexApiClient,
}

impl MediaMetadataService {
    /// Create a new media metadata service
    pub fn new(api_client: PlexApiClient) -> Self {
        Self { api_client }
    }

    /// Fetch media item by rating key
    pub async fn fetch_media_item(&self, rating_key: &str) -> Result<PlexMediaItem> {
        let url = self.api_client.build_url(&format!("/library/metadata/{}", rating_key))?;
        
        // For now, we'll create a mock implementation
        // In a real implementation, this would parse XML from Plex
        self.fetch_media_from_url(&url).await
    }

    /// Get library section for a media item
    pub async fn get_library_section(&self, media_item: &PlexMediaItem) -> Result<Option<LibrarySection>> {
        // Extract section key from the media item's key
        if let Some(section_key) = self.extract_section_key(&media_item.key) {
            let url = self.api_client.build_url(&format!("/library/sections/{}", section_key))?;
            self.fetch_section_from_url(&url).await.map(Some)
        } else {
            Ok(None)
        }
    }

    /// Convert Plex media type to our MediaType enum
    pub fn convert_media_type(&self, plex_type: &str, key: &str) -> MediaType {
        if key.starts_with("/livetv") {
            MediaType::LiveEpisode
        } else {
            match plex_type {
                "movie" => MediaType::Movie,
                "episode" => MediaType::Episode,
                "track" => MediaType::Track,
                "clip" => MediaType::Clip,
                _ => MediaType::Movie, // Default fallback
            }
        }
    }

    /// Extract external GUIDs as a HashMap
    pub fn extract_guids(&self, media_item: &PlexMediaItem) -> HashMap<String, String> {
        let mut guids = HashMap::new();
        
        for guid in &media_item.guids {
            if let Some((provider, id)) = self.parse_guid(&guid.id) {
                guids.insert(provider, id);
            }
        }

        guids
    }

    /// Parse a GUID string into provider and ID
    fn parse_guid(&self, guid: &str) -> Option<(String, String)> {
        if let Some(pos) = guid.find("://") {
            let provider = guid[..pos].to_string();
            let id = guid[pos + 3..].to_string();
            
            // Map some provider names to standard ones
            let mapped_provider = match provider.as_str() {
                "imdb" => "imdb",
                "tmdb" => "tmdb",
                "thetvdb" => "tvdb",
                "musicbrainz" => "mbid",
                _ => &provider,
            };
            
            Some((mapped_provider.to_string(), id))
        } else {
            None
        }
    }

    /// Extract section key from media key
    fn extract_section_key(&self, key: &str) -> Option<String> {
        // Media keys typically look like "/library/metadata/12345"
        // Section keys are in the path like "/library/sections/1/all"
        if key.starts_with("/library/metadata/") {
            // For now, we'll need to make an additional API call to get the section
            // This is a simplified implementation
            None
        } else {
            None
        }
    }

    /// Fetch media from URL (placeholder implementation)
    async fn fetch_media_from_url(&self, _url: &str) -> Result<PlexMediaItem> {
        // This is a placeholder implementation
        // In a real implementation, this would:
        // 1. Make HTTP request to the URL
        // 2. Parse the XML response
        // 3. Convert to PlexMediaItem struct
        
        Err(PlexError::ApiRequestFailed("Media fetching not yet implemented".to_string()).into())
    }

    /// Fetch section from URL (placeholder implementation)
    async fn fetch_section_from_url(&self, _url: &str) -> Result<LibrarySection> {
        // This is a placeholder implementation
        // In a real implementation, this would parse XML from Plex
        
        Err(PlexError::ApiRequestFailed("Section fetching not yet implemented".to_string()).into())
    }

    /// Get poster URL for media item
    pub fn get_poster_url(&self, media_item: &PlexMediaItem) -> Option<String> {
        let thumb = match media_item.media_type.as_str() {
            "movie" => media_item.thumb.as_ref(),
            "episode" => media_item.grandparent_thumb.as_ref(),
            "track" => media_item.thumb.as_ref(), // Album art
            _ => media_item.thumb.as_ref(),
        };

        thumb.map(|t| self.api_client.build_url(t).unwrap_or_default())
    }

    /// Get artist image URL for music tracks
    pub fn get_artist_image_url(&self, media_item: &PlexMediaItem) -> Option<String> {
        if media_item.media_type == "track" {
            media_item.grandparent_thumb.as_ref()
                .map(|t| self.api_client.build_url(t).unwrap_or_default())
        } else {
            None
        }
    }

    /// Get formatted title for display
    pub fn get_display_title(&self, media_item: &PlexMediaItem) -> String {
        match media_item.media_type.as_str() {
            "movie" => media_item.title.clone(),
            "episode" => media_item.grandparent_title.as_ref()
                .unwrap_or(&media_item.title).clone(),
            "track" => media_item.title.clone(),
            _ => media_item.title.clone(),
        }
    }

    /// Get artist name for music tracks
    pub fn get_artist_name(&self, media_item: &PlexMediaItem) -> Option<String> {
        if media_item.media_type == "track" {
            media_item.original_title.as_ref()
                .or(media_item.grandparent_title.as_ref())
                .cloned()
        } else {
            None
        }
    }

    /// Get album name for music tracks
    pub fn get_album_name(&self, media_item: &PlexMediaItem) -> Option<String> {
        if media_item.media_type == "track" {
            media_item.parent_title.clone()
        } else {
            None
        }
    }

    /// Get episode information for TV shows
    pub fn get_episode_info(&self, media_item: &PlexMediaItem) -> Option<(i32, i32, String)> {
        if media_item.media_type == "episode" {
            if let (Some(season), Some(episode)) = (media_item.parent_index, media_item.index) {
                let episode_title = media_item.title.clone();
                Some((season, episode, episode_title))
            } else {
                None
            }
        } else {
            None
        }
    }

    /// Get genre list for movies
    pub fn get_genres(&self, media_item: &PlexMediaItem) -> Vec<String> {
        media_item.genres.iter()
            .map(|g| g.tag.clone())
            .collect()
    }

    /// Check if media item is from a live TV source
    pub fn is_live_tv(&self, media_item: &PlexMediaItem) -> bool {
        media_item.key.starts_with("/livetv")
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_guid_parsing() {
        let service = create_test_service();
        
        let (provider, id) = service.parse_guid("imdb://tt1234567").unwrap();
        assert_eq!(provider, "imdb");
        assert_eq!(id, "tt1234567");
        
        let (provider, id) = service.parse_guid("tmdb://12345").unwrap();
        assert_eq!(provider, "tmdb");
        assert_eq!(id, "12345");
        
        assert!(service.parse_guid("invalid_guid").is_none());
    }

    #[test]
    fn test_media_type_conversion() {
        let service = create_test_service();
        
        assert_eq!(service.convert_media_type("movie", "/library/metadata/123"), MediaType::Movie);
        assert_eq!(service.convert_media_type("episode", "/library/metadata/456"), MediaType::Episode);
        assert_eq!(service.convert_media_type("episode", "/livetv/sessions/789"), MediaType::LiveEpisode);
        assert_eq!(service.convert_media_type("track", "/library/metadata/101"), MediaType::Track);
    }

    fn create_test_service() -> MediaMetadataService {
        let api_client = PlexApiClient::new("test_token".to_string()).unwrap();
        MediaMetadataService::new(api_client)
    }
}

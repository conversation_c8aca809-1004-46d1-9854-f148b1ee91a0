//! Logging utilities for the application

use crate::constants;
use crate::Result;
use log::{Level, LevelFilter};
use std::fs::OpenOptions;
use std::io::Write;

/// Initialize the logging system
pub fn init_logger(debug: bool, write_to_file: bool) -> Result<()> {
    let level = if debug {
        LevelFilter::Debug
    } else {
        LevelFilter::Info
    };

    let mut builder = env_logger::Builder::from_default_env();
    builder
        .filter_level(level)
        .format(|buf, record| {
            writeln!(
                buf,
                "[{}] [{}] {}",
                chrono::Local::now().format("%d-%m-%Y %I:%M:%S %p"),
                record.level(),
                record.args()
            )
        });

    if write_to_file {
        // Ensure data directory exists
        std::fs::create_dir_all(constants::DATA_DIRECTORY)?;
        
        // Set up file logging
        let log_file = OpenOptions::new()
            .create(true)
            .append(true)
            .open(constants::LOG_FILE_PATH)?;
        
        builder.target(env_logger::Target::Pipe(Box::new(log_file)));
    }

    builder.init();

    log::info!("{} - v{}", constants::NAME, constants::VERSION);
    Ok(())
}

/// Logger with prefix for specific components
pub struct PrefixLogger {
    prefix: String,
}

impl PrefixLogger {
    pub fn new(prefix: impl Into<String>) -> Self {
        Self {
            prefix: prefix.into(),
        }
    }

    pub fn info(&self, message: &str) {
        log::info!("{}{}", self.prefix, message);
    }

    pub fn warn(&self, message: &str) {
        log::warn!("{}{}", self.prefix, message);
    }

    pub fn error(&self, message: &str) {
        log::error!("{}{}", self.prefix, message);
    }

    pub fn debug(&self, message: &str) {
        log::debug!("{}{}", self.prefix, message);
    }

    pub fn info_fmt(&self, args: std::fmt::Arguments) {
        log::info!("{}{}", self.prefix, args);
    }

    pub fn warn_fmt(&self, args: std::fmt::Arguments) {
        log::warn!("{}{}", self.prefix, args);
    }

    pub fn error_fmt(&self, args: std::fmt::Arguments) {
        log::error!("{}{}", self.prefix, args);
    }

    pub fn debug_fmt(&self, args: std::fmt::Arguments) {
        log::debug!("{}{}", self.prefix, args);
    }
}

/// Macro for creating formatted log messages with prefix
#[macro_export]
macro_rules! log_with_prefix {
    ($logger:expr, info, $($arg:tt)*) => {
        $logger.info_fmt(format_args!($($arg)*))
    };
    ($logger:expr, warn, $($arg:tt)*) => {
        $logger.warn_fmt(format_args!($($arg)*))
    };
    ($logger:expr, error, $($arg:tt)*) => {
        $logger.error_fmt(format_args!($($arg)*))
    };
    ($logger:expr, debug, $($arg:tt)*) => {
        $logger.debug_fmt(format_args!($($arg)*))
    };
}

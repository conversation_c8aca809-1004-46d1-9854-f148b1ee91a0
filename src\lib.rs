//! Discord Rich Presence for Plex
//! 
//! A Rust implementation of Discord Rich Presence integration for Plex Media Server.
//! This library provides real-time status updates to Discord based on your Plex activity.

pub mod cache;
pub mod config;
pub mod discord;
pub mod error;
pub mod images;
pub mod plex;
pub mod utils;

pub use error::{<PERSON><PERSON><PERSON>, Result};

/// Application constants
pub mod constants {
    pub const NAME: &str = "Discord Rich Presence for Plex";
    pub const VERSION: &str = env!("CARGO_PKG_VERSION");
    pub const PLEX_CLIENT_ID: &str = "discord-rich-presence-plex";
    pub const DISCORD_CLIENT_ID: &str = "413407336082833418";
    
    pub const DATA_DIRECTORY: &str = "data";
    pub const CONFIG_FILE_BASE: &str = "data/config";
    pub const CACHE_FILE_PATH: &str = "data/cache.json";
    pub const LOG_FILE_PATH: &str = "data/console.log";
}

name: Close pull requests
on:
  pull_request_target:
  workflow_dispatch:
    inputs:
      prNumber:
        type: string
        required: true
        description: Pull Request Number
jobs:
  comment-and-close:
    runs-on: ubuntu-latest
    permissions:
      pull-requests: write
    env:
      GH_TOKEN: ${{ github.token }}
      GH_REPO: ${{ github.repository }}
      PR_NUMBER: ${{ inputs.prNumber || github.event.number }}
    steps:
      - name: Comment
        run: gh pr comment ${{ env.PR_NUMBER }} --body "This repository does not accept pull requests."
      - name: Close
        run: gh pr close ${{ env.PR_NUMBER }}

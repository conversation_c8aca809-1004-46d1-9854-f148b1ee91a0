//! Helper functions and utilities

use crate::Result;
use regex::Regex;
use std::time::{SystemTime, UNIX_EPOCH};

/// Format seconds into a human-readable duration string
pub fn format_seconds(seconds: f64, joiner: Option<&str>) -> String {
    let seconds = seconds.round() as u64;
    let hours = seconds / 3600;
    let minutes = (seconds / 60) % 60;
    let secs = seconds % 60;

    match joiner {
        Some(joiner) => {
            if hours == 0 {
                format!("{:02}{}{:02}", minutes, joiner, secs)
            } else {
                format!("{:02}{}{:02}{}{:02}", hours, joiner, minutes, joiner, secs)
            }
        }
        None => {
            let mut result = String::new();
            if hours > 0 {
                result.push_str(&format!("{}h", hours));
            }
            if minutes > 0 {
                result.push_str(&format!("{}m", minutes));
            }
            if secs > 0 || result.is_empty() {
                result.push_str(&format!("{}s", secs));
            }
            result
        }
    }
}

/// Truncate text to a maximum length, adding ellipsis if needed
pub fn truncate(text: &str, max_length: usize) -> String {
    if text.len() > max_length {
        if max_length <= 3 {
            "...".to_string()
        } else {
            format!("{}...", &text[..max_length - 3])
        }
    } else {
        text.to_string()
    }
}

/// Strip non-ASCII characters from text
pub fn strip_non_ascii(text: &str) -> String {
    lazy_static::lazy_static! {
        static ref NON_ASCII_RE: Regex = Regex::new(r"[^\x00-\x7f]").unwrap();
    }
    NON_ASCII_RE.replace_all(text, "").to_string()
}

/// Get current timestamp in milliseconds
pub fn current_timestamp_ms() -> u64 {
    SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap_or_default()
        .as_millis() as u64
}

/// Check if the application is running in a container
pub fn is_in_container() -> bool {
    std::env::var("DRPP_IS_IN_CONTAINER")
        .map(|v| v == "true")
        .unwrap_or(false)
}

/// Check if the application is running interactively
pub fn is_interactive() -> bool {
    atty::is(atty::Stream::Stdin)
}

/// Get the runtime directory for IPC pipes
pub fn get_runtime_directory() -> String {
    if is_in_container() {
        "/run/app".to_string()
    } else {
        std::env::var("XDG_RUNTIME_DIR")
            .or_else(|_| std::env::var("TMPDIR"))
            .or_else(|_| std::env::var("TMP"))
            .or_else(|_| std::env::var("TEMP"))
            .unwrap_or_else(|_| "/tmp".to_string())
    }
}

/// Get the IPC pipe base path
pub fn get_ipc_pipe_base() -> String {
    if cfg!(unix) {
        get_runtime_directory()
    } else {
        r"\\?\pipe".to_string()
    }
}

/// Ensure a directory exists, creating it if necessary
pub fn ensure_directory_exists(path: &str) -> Result<()> {
    if !std::path::Path::new(path).exists() {
        std::fs::create_dir_all(path)?;
    }
    Ok(())
}

/// Get process ID
pub fn get_process_id() -> u32 {
    std::process::id()
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_format_seconds() {
        assert_eq!(format_seconds(0.0, None), "0s");
        assert_eq!(format_seconds(30.0, None), "30s");
        assert_eq!(format_seconds(90.0, None), "1m30s");
        assert_eq!(format_seconds(3661.0, None), "1h1m1s");
        
        assert_eq!(format_seconds(90.0, Some(":")), "01:30");
        assert_eq!(format_seconds(3661.0, Some(":")), "01:01:01");
    }

    #[test]
    fn test_truncate() {
        assert_eq!(truncate("hello", 10), "hello");
        assert_eq!(truncate("hello world", 8), "hello...");
        assert_eq!(truncate("hi", 2), "hi");
        assert_eq!(truncate("hello", 3), "...");
    }

    #[test]
    fn test_strip_non_ascii() {
        assert_eq!(strip_non_ascii("hello"), "hello");
        assert_eq!(strip_non_ascii("héllo"), "hllo");
        assert_eq!(strip_non_ascii("🎵 music"), " music");
    }
}

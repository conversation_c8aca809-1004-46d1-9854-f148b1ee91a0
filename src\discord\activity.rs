//! Discord Rich Presence activity builder

use crate::config::{ButtonConfig, DisplayConfig, ProgressMode};
use crate::utils::{current_timestamp_ms, truncate};
use discord_rich_presence::activity::{Activity, ActivityType, Assets, Button, Timestamps};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// Media types supported by the application
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
#[serde(rename_all = "snake_case")]
pub enum MediaType {
    Movie,
    Episode,
    LiveEpisode,
    Track,
    Clip,
}

impl MediaType {
    /// Convert to Discord activity type
    pub fn to_activity_type(&self) -> ActivityType {
        match self {
            MediaType::Movie | MediaType::Episode | MediaType::LiveEpisode | MediaType::Clip => {
                ActivityType::Watching
            }
            MediaType::Track => ActivityType::Listening,
        }
    }

    /// Check if this media type supports duration display
    pub fn supports_duration(&self) -> bool {
        !matches!(self, MediaType::Track)
    }
}

/// Media playback state
#[derive(Debug, <PERSON>lone, PartialEq)]
pub enum PlaybackState {
    Playing,
    Paused,
    Stopped,
    Buffering,
}

impl PlaybackState {
    /// Get the status icon name for this state
    pub fn status_icon(&self) -> &'static str {
        match self {
            PlaybackState::Playing => "playing",
            PlaybackState::Paused => "paused",
            PlaybackState::Stopped => "stopped",
            PlaybackState::Buffering => "buffering",
        }
    }

    /// Get the capitalized state name
    pub fn display_name(&self) -> &'static str {
        match self {
            PlaybackState::Playing => "Playing",
            PlaybackState::Paused => "Paused",
            PlaybackState::Stopped => "Stopped",
            PlaybackState::Buffering => "Buffering",
        }
    }
}

/// Media information for building activities
#[derive(Debug, Clone)]
pub struct MediaInfo {
    pub media_type: MediaType,
    pub title: String,
    pub state: PlaybackState,
    pub duration: Option<u64>, // in milliseconds
    pub view_offset: Option<u64>, // in milliseconds
    pub year: Option<i32>,
    pub genres: Vec<String>,
    pub album: Option<String>,
    pub artist: Option<String>,
    pub season: Option<i32>,
    pub episode: Option<i32>,
    pub episode_title: Option<String>,
    pub poster_url: Option<String>,
    pub artist_image_url: Option<String>,
    pub guids: HashMap<String, String>, // External IDs (imdb, tmdb, etc.)
}

/// Activity builder for Discord Rich Presence
pub struct ActivityBuilder<'a> {
    config: &'a DisplayConfig,
    media: &'a MediaInfo,
}

impl<'a> ActivityBuilder<'a> {
    /// Create a new activity builder
    pub fn new(config: &'a DisplayConfig, media: &'a MediaInfo) -> Self {
        Self { config, media }
    }

    /// Build the Discord activity
    pub fn build(&self) -> Activity {
        let mut activity = Activity::new()
            .activity_type(self.media.media_type.to_activity_type())
            .details(&self.build_details());

        // Add state information
        if let Some(state) = self.build_state() {
            activity = activity.state(&state);
        }

        // Add assets (images and status icons)
        if let Some(assets) = self.build_assets() {
            activity = activity.assets(assets);
        }

        // Add timestamps for progress tracking
        if let Some(timestamps) = self.build_timestamps() {
            activity = activity.timestamps(timestamps);
        }

        // Add buttons
        if let Some(buttons) = self.build_buttons() {
            activity = activity.buttons(buttons);
        }

        activity
    }

    /// Build the details (main title) for the activity
    fn build_details(&self) -> String {
        let mut title = self.media.title.clone();

        // Add year for movies and shows
        if self.config.year {
            if let Some(year) = self.media.year {
                match self.media.media_type {
                    MediaType::Movie => {
                        title = format!("{} ({})", title, year);
                    }
                    MediaType::Episode | MediaType::LiveEpisode => {
                        title = format!("{} ({})", title, year);
                    }
                    _ => {}
                }
            }
        }

        truncate(&title, 120)
    }

    /// Build the state (subtitle) for the activity
    fn build_state(&self) -> Option<String> {
        let mut state_parts = Vec::new();

        match self.media.media_type {
            MediaType::Movie => {
                // Add duration
                if self.config.duration {
                    if let Some(duration) = self.media.duration {
                        state_parts.push(crate::utils::format_seconds(duration as f64 / 1000.0, None));
                    }
                }

                // Add genres
                if self.config.genres && !self.media.genres.is_empty() {
                    let genres = self.media.genres.iter()
                        .take(3)
                        .cloned()
                        .collect::<Vec<_>>()
                        .join(", ");
                    state_parts.push(genres);
                }
            }
            MediaType::Episode => {
                // Add season/episode info
                if let (Some(season), Some(episode)) = (self.media.season, self.media.episode) {
                    state_parts.push(format!("S{:02}E{:02}", season, episode));
                }

                // Add episode title
                if let Some(episode_title) = &self.media.episode_title {
                    state_parts.push(episode_title.clone());
                }

                // Add duration
                if self.config.duration {
                    if let Some(duration) = self.media.duration {
                        state_parts.push(crate::utils::format_seconds(duration as f64 / 1000.0, None));
                    }
                }
            }
            MediaType::LiveEpisode => {
                // Add episode title if different from show title
                if let Some(episode_title) = &self.media.episode_title {
                    if episode_title != &self.media.title {
                        state_parts.push(episode_title.clone());
                    }
                }
            }
            MediaType::Track => {
                // Add artist
                if self.config.artist {
                    if let Some(artist) = &self.media.artist {
                        state_parts.push(artist.clone());
                    }
                }
            }
            MediaType::Clip => {
                // Add duration
                if self.config.duration {
                    if let Some(duration) = self.media.duration {
                        state_parts.push(crate::utils::format_seconds(duration as f64 / 1000.0, None));
                    }
                }
            }
        }

        // Add playback progress for non-playing states
        if self.media.state != PlaybackState::Playing && self.media.media_type.supports_duration() {
            if let (Some(duration), Some(view_offset)) = (self.media.duration, self.media.view_offset) {
                let progress_text = match self.config.progress_mode {
                    ProgressMode::Remaining => {
                        let remaining = (duration - view_offset) as f64 / 1000.0;
                        format!("{} left", crate::utils::format_seconds(remaining, Some(":")))
                    }
                    _ => {
                        let elapsed = view_offset as f64 / 1000.0;
                        format!("{} elapsed", crate::utils::format_seconds(elapsed, Some(":")))
                    }
                };
                state_parts.push(progress_text);
            }
        }

        // Add status if not using status icon
        if !self.config.status_icon && self.media.state != PlaybackState::Playing {
            state_parts.push(self.media.state.display_name().to_string());
        }

        if state_parts.is_empty() {
            None
        } else {
            Some(truncate(&state_parts.join(" · "), 120))
        }
    }

    /// Build assets (images and icons)
    fn build_assets(&self) -> Option<Assets> {
        let mut assets = Assets::new();
        let mut has_assets = false;

        // Large image (poster/album art)
        match self.media.media_type {
            MediaType::Track => {
                if self.config.album_image {
                    if let Some(poster_url) = &self.media.poster_url {
                        assets = assets.large_image(poster_url);
                        has_assets = true;
                    }
                }
                
                // Large text (album name)
                if self.config.album {
                    if let Some(album) = &self.media.album {
                        let mut album_text = album.clone();
                        if self.config.year {
                            if let Some(year) = self.media.year {
                                album_text = format!("{} ({})", truncate(&album_text, 110), year);
                            }
                        }
                        assets = assets.large_text(&truncate(&album_text, 120));
                        has_assets = true;
                    }
                }
            }
            _ => {
                if self.config.posters.enabled {
                    if let Some(poster_url) = &self.media.poster_url {
                        assets = assets.large_image(poster_url);
                        has_assets = true;
                    }
                }
            }
        }

        // Small image and text
        if self.config.status_icon {
            assets = assets.small_image(self.media.state.status_icon());
            assets = assets.small_text(self.media.state.display_name());
            has_assets = true;
        } else if self.media.media_type == MediaType::Track && self.config.artist_image {
            if let Some(artist_image_url) = &self.media.artist_image_url {
                assets = assets.small_image(artist_image_url);
                has_assets = true;
            }
            if let Some(artist) = &self.media.artist {
                assets = assets.small_text(&truncate(artist, 120));
                has_assets = true;
            }
        }

        if has_assets {
            Some(assets)
        } else {
            None
        }
    }

    /// Build timestamps for progress tracking
    fn build_timestamps(&self) -> Option<Timestamps> {
        if self.media.state != PlaybackState::Playing {
            return None;
        }

        let (duration, view_offset) = match (self.media.duration, self.media.view_offset) {
            (Some(d), Some(v)) => (d, v),
            _ => return None,
        };

        let current_time = current_timestamp_ms() as i64;
        
        match self.config.progress_mode {
            ProgressMode::Off => None,
            ProgressMode::Elapsed => {
                let start_time = current_time - view_offset as i64;
                Some(Timestamps::new().start(start_time))
            }
            ProgressMode::Remaining => {
                let end_time = current_time + (duration - view_offset) as i64;
                Some(Timestamps::new().end(end_time))
            }
            ProgressMode::Bar => {
                let start_time = current_time - view_offset as i64;
                let end_time = current_time + (duration - view_offset) as i64;
                Some(Timestamps::new().start(start_time).end(end_time))
            }
        }
    }

    /// Build buttons for external links
    fn build_buttons(&self) -> Option<Vec<Button>> {
        if !self.config.buttons.is_empty() {
            let buttons = self.config.buttons.iter()
                .filter(|button| {
                    // Check if button applies to this media type
                    button.media_types.is_empty() || 
                    button.media_types.contains(&format!("{:?}", self.media.media_type).to_lowercase())
                })
                .filter_map(|button| self.build_button(button))
                .take(2) // Discord only supports 2 buttons
                .collect::<Vec<_>>();
            
            if buttons.is_empty() {
                None
            } else {
                Some(buttons)
            }
        } else {
            None
        }
    }

    /// Build a single button
    fn build_button(&self, button_config: &ButtonConfig) -> Option<Button> {
        let label = button_config.label.replace("{title}", &crate::utils::strip_non_ascii(&self.media.title));
        let label = truncate(&label, 30);

        let url = if button_config.url.starts_with("dynamic:") {
            self.build_dynamic_url(&button_config.url[8..])?
        } else {
            button_config.url.clone()
        };

        Some(Button::new(&label, &url))
    }

    /// Build dynamic URLs for external services
    fn build_dynamic_url(&self, service: &str) -> Option<String> {
        match service {
            "imdb" => {
                let imdb_id = self.media.guids.get("imdb")?;
                Some(format!("https://www.imdb.com/title/{}", imdb_id))
            }
            "tmdb" => {
                let tmdb_id = self.media.guids.get("tmdb")?;
                let path = match self.media.media_type {
                    MediaType::Movie => "movie",
                    _ => "tv",
                };
                Some(format!("https://www.themoviedb.org/{}/{}", path, tmdb_id))
            }
            "thetvdb" => {
                let tvdb_id = self.media.guids.get("tvdb")?;
                let path = match self.media.media_type {
                    MediaType::Movie => "movie",
                    _ => "series",
                };
                Some(format!("https://www.thetvdb.com/dereferrer/{}/{}", path, tvdb_id))
            }
            "trakt" => {
                let tmdb_id = self.media.guids.get("tmdb")?;
                let id_type = match self.media.media_type {
                    MediaType::Movie => "movie",
                    _ => "show",
                };
                Some(format!("https://trakt.tv/search/tmdb/{}?id_type={}", tmdb_id, id_type))
            }
            "letterboxd" => {
                if self.media.media_type == MediaType::Movie {
                    let tmdb_id = self.media.guids.get("tmdb")?;
                    Some(format!("https://letterboxd.com/tmdb/{}", tmdb_id))
                } else {
                    None
                }
            }
            "musicbrainz" => {
                let mbid = self.media.guids.get("mbid")?;
                Some(format!("https://musicbrainz.org/track/{}", mbid))
            }
            _ => None,
        }
    }
}

### Release Notes

- Added config property `display.posters.fit` to make posters fit inside a square while maintaining the original aspect ratio. This is set to `true` by default. Otherwise, Discord crops posters into a square.

### Installation Instructions

- [Regular](https://github.com/phin05/discord-rich-presence-plex/blob/v2.12.0/README.md#installation)
- [Docker](https://github.com/phin05/discord-rich-presence-plex/blob/v2.12.0/README.md#run-with-docker)

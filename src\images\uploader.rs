//! Image upload functionality

use crate::cache::CacheStorage;
use crate::config::PostersConfig;
use crate::error::{ImageError, Result};
use crate::images::ImageProcessor;
use reqwest::{multipart, Client};
use serde::{Deserialize, Serialize};
use std::time::{SystemTime, UNIX_EPOCH};

/// Response from Imgur API
#[derive(Debug, Deserialize)]
struct ImgurResponse {
    success: bool,
    status: u16,
    data: ImgurData,
}

#[derive(Debug, Deserialize)]
struct ImgurData {
    #[serde(default)]
    error: String,
    #[serde(default)]
    link: String,
}

/// Image uploader service
pub struct ImageUploader {
    config: PostersConfig,
    processor: ImageProcessor,
    cache: CacheStorage,
    client: Client,
}

impl ImageUploader {
    /// Create a new image uploader
    pub fn new(config: PostersConfig, cache: CacheStorage) -> Result<Self> {
        let processor = ImageProcessor::new(config.clone());
        let client = Client::builder()
            .timeout(std::time::Duration::from_secs(30))
            .build()
            .map_err(|e| ImageError::UploadFailed(format!("Failed to create HTTP client: {}", e)))?;

        Ok(Self {
            config,
            processor,
            cache,
            client,
        })
    }

    /// Upload an image and return the URL
    pub async fn upload(&self, key: &str, image_url: &str) -> Result<Option<String>> {
        // Check cache first
        if let Some(cached_url) = self.cache.get(key) {
            log::debug!("Found cached image URL for key: {}", key);
            return Ok(Some(cached_url));
        }

        // Download the image
        log::debug!("Downloading image from: {}", image_url);
        let image_data = self.download_image(image_url).await?;

        // Validate and process the image
        self.processor.validate_image_size(&image_data)?;
        
        if !self.processor.is_supported_format(&image_data) {
            return Err(ImageError::InvalidFormat("Unsupported image format".to_string()).into());
        }

        let processed_data = self.processor.optimize_for_discord(&image_data)?;

        // Upload to the appropriate service
        let uploaded_url = if !self.config.imgur_client_id.is_empty() {
            self.upload_to_imgur(&processed_data).await?
        } else {
            self.upload_to_litterbox(&processed_data).await?
        };

        // Cache the result
        let expiry = if !self.config.imgur_client_id.is_empty() {
            0 // Imgur links don't expire
        } else {
            // Litterbox links expire in 72 hours
            SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs() + (72 * 60 * 60)
        };

        self.cache.set(key.to_string(), uploaded_url.clone(), expiry)?;
        
        log::info!("Successfully uploaded image for key: {}", key);
        Ok(Some(uploaded_url))
    }

    /// Download image from URL
    async fn download_image(&self, url: &str) -> Result<Vec<u8>> {
        let response = self.client
            .get(url)
            .send()
            .await
            .map_err(|e| ImageError::UploadFailed(format!("Failed to download image: {}", e)))?;

        if !response.status().is_success() {
            return Err(ImageError::UploadFailed(format!(
                "Failed to download image: HTTP {}",
                response.status()
            )).into());
        }

        let bytes = response
            .bytes()
            .await
            .map_err(|e| ImageError::UploadFailed(format!("Failed to read image data: {}", e)))?;

        Ok(bytes.to_vec())
    }

    /// Upload image to Imgur
    async fn upload_to_imgur(&self, image_data: &[u8]) -> Result<String> {
        log::debug!("Uploading image to Imgur");

        let form = multipart::Form::new()
            .part("image", multipart::Part::bytes(image_data.to_vec())
                .file_name("image.png")
                .mime_str("image/png")
                .map_err(|e| ImageError::UploadFailed(format!("Failed to create form part: {}", e)))?);

        let response = self.client
            .post("https://api.imgur.com/3/image")
            .header("Authorization", format!("Client-ID {}", self.config.imgur_client_id))
            .multipart(form)
            .send()
            .await
            .map_err(|e| ImageError::UploadFailed(format!("Imgur upload request failed: {}", e)))?;

        let status = response.status();
        let response_text = response
            .text()
            .await
            .map_err(|e| ImageError::UploadFailed(format!("Failed to read Imgur response: {}", e)))?;

        log::debug!("Imgur response: HTTP {}, {}", status, response_text);

        let imgur_response: ImgurResponse = serde_json::from_str(&response_text)
            .map_err(|e| ImageError::UploadFailed(format!("Failed to parse Imgur response: {}", e)))?;

        if !imgur_response.success {
            return Err(ImageError::UploadFailed(format!(
                "Imgur upload failed: {}",
                imgur_response.data.error
            )).into());
        }

        if imgur_response.data.link.is_empty() {
            return Err(ImageError::UploadFailed("Imgur returned empty link".to_string()).into());
        }

        Ok(imgur_response.data.link)
    }

    /// Upload image to Litterbox
    async fn upload_to_litterbox(&self, image_data: &[u8]) -> Result<String> {
        log::debug!("Uploading image to Litterbox");

        let form = multipart::Form::new()
            .text("reqtype", "fileupload")
            .text("time", "72h")
            .part("fileToUpload", multipart::Part::bytes(image_data.to_vec())
                .file_name("image.png")
                .mime_str("image/png")
                .map_err(|e| ImageError::UploadFailed(format!("Failed to create form part: {}", e)))?);

        let response = self.client
            .post("https://litterbox.catbox.moe/resources/internals/api.php")
            .multipart(form)
            .send()
            .await
            .map_err(|e| ImageError::UploadFailed(format!("Litterbox upload request failed: {}", e)))?;

        let status = response.status();
        let response_text = response
            .text()
            .await
            .map_err(|e| ImageError::UploadFailed(format!("Failed to read Litterbox response: {}", e)))?;

        log::debug!("Litterbox response: HTTP {}, {}", status, response_text);

        let url = response_text.trim();
        
        if !url.starts_with("http") {
            return Err(ImageError::UploadFailed(format!(
                "Litterbox upload failed: {}",
                url
            )).into());
        }

        Ok(url.to_string())
    }

    /// Clear expired cache entries
    pub async fn cleanup_cache(&self) -> Result<usize> {
        self.cache.cleanup_expired()
    }

    /// Get cache statistics
    pub fn cache_stats(&self) -> (usize, Vec<String>) {
        (self.cache.len(), self.cache.keys())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::PostersConfig;
    use mockito::{mock, Matcher};

    fn create_test_config() -> PostersConfig {
        PostersConfig {
            enabled: true,
            imgur_client_id: "test_client_id".to_string(),
            max_size: 256,
            fit: true,
        }
    }

    #[tokio::test]
    async fn test_uploader_creation() {
        let config = create_test_config();
        let cache = CacheStorage::new().unwrap();
        let uploader = ImageUploader::new(config, cache);
        assert!(uploader.is_ok());
    }

    #[tokio::test]
    async fn test_cache_hit() {
        let config = create_test_config();
        let cache = CacheStorage::new().unwrap();
        
        // Pre-populate cache
        cache.set("test_key".to_string(), "http://example.com/image.png".to_string(), 0).unwrap();
        
        let uploader = ImageUploader::new(config, cache).unwrap();
        let result = uploader.upload("test_key", "http://example.com/original.png").await;
        
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), Some("http://example.com/image.png".to_string()));
    }

    // Note: Full upload tests would require mocking the HTTP services
    // or running integration tests with real services
}

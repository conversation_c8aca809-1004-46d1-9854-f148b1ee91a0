//! Plex API client implementation

use crate::constants;
use crate::error::{PlexError, Result};
use crate::plex::auth::PlexUser;
use reqwest::Client;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::Duration;
use url::Url;

/// Plex server resource information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PlexResource {
    pub name: String,
    pub product: String,
    #[serde(rename = "productVersion")]
    pub product_version: String,
    pub platform: String,
    #[serde(rename = "platformVersion")]
    pub platform_version: String,
    pub device: String,
    #[serde(rename = "clientIdentifier")]
    pub client_identifier: String,
    #[serde(rename = "createdAt")]
    pub created_at: String,
    #[serde(rename = "lastSeenAt")]
    pub last_seen_at: String,
    pub provides: String,
    #[serde(rename = "ownerId")]
    pub owner_id: Option<u64>,
    #[serde(rename = "sourceTitle")]
    pub source_title: Option<String>,
    #[serde(rename = "publicAddress")]
    pub public_address: Option<String>,
    #[serde(rename = "accessToken")]
    pub access_token: Option<String>,
    pub owned: bool,
    pub home: bool,
    pub synced: bool,
    pub relay: bool,
    pub presence: bool,
    #[serde(rename = "httpsRequired")]
    pub https_required: bool,
    #[serde(rename = "publicAddressMatches")]
    pub public_address_matches: bool,
    pub connections: Vec<PlexConnection>,
}

/// Plex server connection information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PlexConnection {
    pub protocol: String,
    pub address: String,
    pub port: u16,
    pub uri: String,
    pub local: bool,
    pub relay: bool,
    #[serde(rename = "IPv6")]
    pub ipv6: bool,
}

/// Plex API client
#[derive(Clone)]
pub struct PlexApiClient {
    client: Client,
    token: String,
    base_url: Option<String>,
}

impl PlexApiClient {
    /// Create a new Plex API client
    pub fn new(token: String) -> Result<Self> {
        let client = Client::builder()
            .timeout(Duration::from_secs(30))
            .build()
            .map_err(|e| PlexError::ConnectionFailed(format!("Failed to create HTTP client: {}", e)))?;

        Ok(Self {
            client,
            token,
            base_url: None,
        })
    }

    /// Set the base URL for a specific Plex server
    pub fn set_server_url(&mut self, url: String) {
        self.base_url = Some(url);
    }

    /// Get user's Plex resources (servers)
    pub async fn get_resources(&self) -> Result<Vec<PlexResource>> {
        let response = self.client
            .get("https://plex.tv/api/v2/resources")
            .header("X-Plex-Token", &self.token)
            .header("X-Plex-Client-Identifier", constants::PLEX_CLIENT_ID)
            .send()
            .await
            .map_err(|e| PlexError::ApiRequestFailed(format!("Failed to get resources: {}", e)))?;

        if !response.status().is_success() {
            return Err(PlexError::ApiRequestFailed(format!(
                "Resources request failed: HTTP {}",
                response.status()
            )).into());
        }

        let resources: Vec<PlexResource> = response
            .json()
            .await
            .map_err(|e| PlexError::InvalidResponse(format!("Failed to parse resources: {}", e)))?;

        Ok(resources)
    }

    /// Find a server by name
    pub async fn find_server(&self, server_name: &str) -> Result<Option<PlexResource>> {
        let resources = self.get_resources().await?;

        for resource in resources {
            if resource.product == "Plex Media Server" &&
               resource.name.to_lowercase() == server_name.to_lowercase() {
                return Ok(Some(resource));
            }
        }

        Ok(None)
    }

    /// Connect to a Plex server
    pub async fn connect_to_server(&mut self, resource: &PlexResource) -> Result<String> {
        // Try connections in order of preference (local first, then remote)
        let mut connections = resource.connections.clone();
        connections.sort_by(|a, b| {
            // Prefer local connections
            match (a.local, b.local) {
                (true, false) => std::cmp::Ordering::Less,
                (false, true) => std::cmp::Ordering::Greater,
                _ => std::cmp::Ordering::Equal,
            }
        });

        for connection in &connections {
            let server_url = connection.uri.clone();
            log::debug!("Trying connection to: {}", server_url);

            // Test the connection
            if self.test_server_connection(&server_url).await.is_ok() {
                self.base_url = Some(server_url.clone());
                log::info!("Connected to Plex server: {}", resource.name);
                return Ok(server_url);
            }
        }

        Err(PlexError::ConnectionFailed(format!(
            "Failed to connect to server: {}",
            resource.name
        )).into())
    }

    /// Test a server connection
    async fn test_server_connection(&self, server_url: &str) -> Result<()> {
        let url = format!("{}/identity", server_url);

        let response = self.client
            .get(&url)
            .header("X-Plex-Token", &self.token)
            .header("X-Plex-Client-Identifier", constants::PLEX_CLIENT_ID)
            .timeout(Duration::from_secs(5))
            .send()
            .await
            .map_err(|e| PlexError::ConnectionFailed(format!("Connection test failed: {}", e)))?;

        if response.status().is_success() {
            Ok(())
        } else {
            Err(PlexError::ConnectionFailed(format!(
                "Server returned HTTP {}",
                response.status()
            )).into())
        }
    }

    /// Get server identity information
    pub async fn get_server_identity(&self) -> Result<ServerIdentity> {
        let base_url = self.base_url.as_ref()
            .ok_or_else(|| PlexError::ConnectionFailed("No server URL set".to_string()))?;

        let url = format!("{}/identity", base_url);

        let response = self.client
            .get(&url)
            .header("X-Plex-Token", &self.token)
            .header("X-Plex-Client-Identifier", constants::PLEX_CLIENT_ID)
            .send()
            .await
            .map_err(|e| PlexError::ApiRequestFailed(format!("Failed to get server identity: {}", e)))?;

        if !response.status().is_success() {
            return Err(PlexError::ApiRequestFailed(format!(
                "Server identity request failed: HTTP {}",
                response.status()
            )).into());
        }

        let identity: ServerIdentity = response
            .json()
            .await
            .map_err(|e| PlexError::InvalidResponse(format!("Failed to parse server identity: {}", e)))?;

        Ok(identity)
    }

    /// Get current sessions on the server
    pub async fn get_sessions(&self) -> Result<Vec<Session>> {
        let base_url = self.base_url.as_ref()
            .ok_or_else(|| PlexError::ConnectionFailed("No server URL set".to_string()))?;

        let url = format!("{}/status/sessions", base_url);

        let response = self.client
            .get(&url)
            .header("X-Plex-Token", &self.token)
            .header("X-Plex-Client-Identifier", constants::PLEX_CLIENT_ID)
            .send()
            .await
            .map_err(|e| PlexError::ApiRequestFailed(format!("Failed to get sessions: {}", e)))?;

        if !response.status().is_success() {
            return Err(PlexError::ApiRequestFailed(format!(
                "Sessions request failed: HTTP {}",
                response.status()
            )).into());
        }

        // Parse the XML response (Plex uses XML for most endpoints)
        let text = response.text().await
            .map_err(|e| PlexError::InvalidResponse(format!("Failed to read sessions response: {}", e)))?;

        // For now, return empty sessions - we'll implement XML parsing later
        // This is a placeholder for the XML parsing implementation
        log::debug!("Sessions response: {}", text);
        Ok(Vec::new())
    }

    /// Build a full URL for a server endpoint
    pub fn build_url(&self, path: &str) -> Result<String> {
        let base_url = self.base_url.as_ref()
            .ok_or_else(|| PlexError::ConnectionFailed("No server URL set".to_string()))?;

        let mut url = Url::parse(base_url)
            .map_err(|e| PlexError::ConnectionFailed(format!("Invalid base URL: {}", e)))?;

        url.set_path(path);
        url.query_pairs_mut()
            .append_pair("X-Plex-Token", &self.token);

        Ok(url.to_string())
    }

    /// Get the WebSocket URL for alerts
    pub fn get_websocket_url(&self) -> Result<String> {
        let base_url = self.base_url.as_ref()
            .ok_or_else(|| PlexError::ConnectionFailed("No server URL set".to_string()))?;

        let mut url = Url::parse(base_url)
            .map_err(|e| PlexError::ConnectionFailed(format!("Invalid base URL: {}", e)))?;

        // Convert HTTP to WebSocket protocol
        let scheme = match url.scheme() {
            "https" => "wss",
            "http" => "ws",
            _ => return Err(PlexError::ConnectionFailed("Invalid URL scheme".to_string()).into()),
        };

        url.set_scheme(scheme)
            .map_err(|_| PlexError::ConnectionFailed("Failed to set WebSocket scheme".to_string()))?;

        url.set_path("/:/websockets/notifications");
        url.query_pairs_mut()
            .append_pair("X-Plex-Token", &self.token);

        Ok(url.to_string())
    }
}

/// Server identity information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerIdentity {
    #[serde(rename = "machineIdentifier")]
    pub machine_identifier: String,
    pub version: String,
}

/// Plex session information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Session {
    #[serde(rename = "sessionKey")]
    pub session_key: String,
    pub usernames: Vec<String>,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_api_client_creation() {
        let client = PlexApiClient::new("test_token".to_string());
        assert!(client.is_ok());
    }

    #[test]
    fn test_url_building() {
        let mut client = PlexApiClient::new("test_token".to_string()).unwrap();
        client.set_server_url("http://localhost:32400".to_string());

        let url = client.build_url("/library/sections").unwrap();
        assert!(url.contains("http://localhost:32400/library/sections"));
        assert!(url.contains("X-Plex-Token=test_token"));
    }

    #[test]
    fn test_websocket_url() {
        let mut client = PlexApiClient::new("test_token".to_string()).unwrap();
        client.set_server_url("http://localhost:32400".to_string());

        let ws_url = client.get_websocket_url().unwrap();
        assert!(ws_url.starts_with("ws://localhost:32400/:/websockets/notifications"));
        assert!(ws_url.contains("X-Plex-Token=test_token"));
    }
}

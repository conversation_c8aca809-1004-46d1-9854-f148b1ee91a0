[package]
name = "discord-rich-presence-plex"
version = "2.14.1"
edition = "2021"
authors = ["Discord Rich Presence for Plex Contributors"]
description = "Discord Rich Presence for Plex - A Rust implementation"
license = "MIT"
repository = "https://github.com/phin05/discord-rich-presence-plex"
keywords = ["discord", "plex", "rich-presence", "media"]
categories = ["multimedia", "api-bindings"]

[dependencies]
# Async runtime
tokio = { version = "1.0", features = ["full"] }

# Discord Rich Presence
discord-rich-presence = "0.2"

# HTTP client and WebSocket
reqwest = { version = "0.12", features = ["json", "multipart", "stream"] }
tokio-tungstenite = { version = "0.24", features = ["native-tls"] }

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
serde_yaml_bw = "0.3"

# Image processing
image = { version = "0.25", features = ["png", "jpeg"] }

# Error handling and utilities
anyhow = "1.0"
thiserror = "1.0"
log = "0.4"
env_logger = "0.11"
uuid = { version = "1.0", features = ["v4", "serde"] }
url = "2.5"
chrono = { version = "0.4", features = ["serde"] }
base64 = "0.22"

# CLI argument parsing
clap = { version = "4.0", features = ["derive"] }

# Additional utilities
regex = "1.10"
futures-util = "0.3"
bytes = "1.0"
lazy_static = "1.4"
atty = "0.2"

[dev-dependencies]
tokio-test = "0.4"
tempfile = "3.0"
mockito = "1.0"

[[bin]]
name = "discord-rich-presence-plex"
path = "src/main.rs"

[profile.release]
lto = true
codegen-units = 1
panic = "abort"
strip = true

//! Discord IPC client implementation

use crate::constants;
use crate::error::{DiscordError, Result};
use crate::utils::{get_ipc_pipe_base, PrefixLogger};
use discord_rich_presence::{activity::Activity, DiscordIpc, DiscordIpcClient};
use std::sync::{Arc, Mutex};
use std::time::{Duration, Instant};
use tokio::time::sleep;

/// Discord IPC service wrapper
#[derive(Debug)]
pub struct DiscordIpcService {
    client: Arc<Mutex<Option<DiscordIpcClient>>>,
    pipe_number: Option<i32>,
    logger: PrefixLogger,
    last_connection_attempt: Arc<Mutex<Option<Instant>>>,
    connection_retry_delay: Duration,
}

impl DiscordIpcService {
    /// Create a new Discord IPC service
    pub fn new(pipe_number: Option<i32>) -> Self {
        let logger = PrefixLogger::new("[Discord] ");
        
        Self {
            client: Arc::new(Mutex::new(None)),
            pipe_number,
            logger,
            last_connection_attempt: Arc::new(Mutex::new(None)),
            connection_retry_delay: Duration::from_secs(5),
        }
    }

    /// Check if connected to Discord
    pub fn is_connected(&self) -> bool {
        if let Ok(client_guard) = self.client.lock() {
            client_guard.is_some()
        } else {
            false
        }
    }

    /// Connect to Discord IPC
    pub async fn connect(&self) -> Result<()> {
        // Check if we should wait before retrying
        if let Ok(last_attempt) = self.last_connection_attempt.lock() {
            if let Some(last) = *last_attempt {
                let elapsed = last.elapsed();
                if elapsed < self.connection_retry_delay {
                    let wait_time = self.connection_retry_delay - elapsed;
                    log::debug!("Waiting {:?} before retry", wait_time);
                    sleep(wait_time).await;
                }
            }
        }

        // Update last attempt time
        if let Ok(mut last_attempt) = self.last_connection_attempt.lock() {
            *last_attempt = Some(Instant::now());
        }

        self.logger.info("Connecting to Discord IPC");

        // Try to create a new client
        let mut client = DiscordIpcClient::new(constants::DISCORD_CLIENT_ID)
            .map_err(|e| DiscordError::ConnectionFailed)?;

        // Attempt connection
        client.connect()
            .map_err(|e| {
                self.logger.error(&format!("Failed to connect to Discord IPC: {}", e));
                DiscordError::ConnectionFailed
            })?;

        // Store the connected client
        if let Ok(mut client_guard) = self.client.lock() {
            *client_guard = Some(client);
        } else {
            return Err(DiscordError::ConnectionFailed.into());
        }

        self.logger.info("Successfully connected to Discord IPC");
        Ok(())
    }

    /// Disconnect from Discord IPC
    pub fn disconnect(&self) {
        if let Ok(mut client_guard) = self.client.lock() {
            if let Some(mut client) = client_guard.take() {
                if let Err(e) = client.close() {
                    self.logger.error(&format!("Error during disconnect: {}", e));
                } else {
                    self.logger.info("Disconnected from Discord IPC");
                }
            } else {
                self.logger.warn("Attempted to disconnect when not connected");
            }
        }
    }

    /// Set Discord activity
    pub async fn set_activity(&self, activity: Activity) -> Result<()> {
        // Ensure we're connected
        if !self.is_connected() {
            self.connect().await?;
        }

        if let Ok(mut client_guard) = self.client.lock() {
            if let Some(client) = client_guard.as_mut() {
                client.set_activity(activity)
                    .map_err(|e| {
                        self.logger.error(&format!("Failed to set activity: {}", e));
                        // Clear the client on error as it might be in a bad state
                        *client_guard = None;
                        DiscordError::ActivityFailed(e.to_string())
                    })?;
                
                log::debug!("Discord activity updated successfully");
                Ok(())
            } else {
                Err(DiscordError::NotConnected.into())
            }
        } else {
            Err(DiscordError::NotConnected.into())
        }
    }

    /// Clear Discord activity
    pub async fn clear_activity(&self) -> Result<()> {
        if !self.is_connected() {
            return Ok(()); // Nothing to clear if not connected
        }

        if let Ok(mut client_guard) = self.client.lock() {
            if let Some(client) = client_guard.as_mut() {
                client.clear_activity()
                    .map_err(|e| {
                        self.logger.error(&format!("Failed to clear activity: {}", e));
                        *client_guard = None;
                        DiscordError::ActivityFailed(e.to_string())
                    })?;
                
                log::debug!("Discord activity cleared");
                Ok(())
            } else {
                Ok(())
            }
        } else {
            Ok(())
        }
    }

    /// Reconnect to Discord IPC
    pub async fn reconnect(&self) -> Result<()> {
        self.logger.info("Reconnecting to Discord IPC");
        self.disconnect();
        self.connect().await
    }

    /// Test the IPC connection with a sample activity
    pub async fn test_connection(&self) -> Result<()> {
        use discord_rich_presence::activity::{Activity, Assets, Button, Timestamps};
        
        self.logger.info("Testing Discord IPC connection");
        
        let test_activity = Activity::new()
            .state("Testing connection")
            .details("Discord Rich Presence for Plex")
            .assets(Assets::new().large_image("paused").large_text("Test"))
            .timestamps(Timestamps::new().start(
                std::time::SystemTime::now()
                    .duration_since(std::time::UNIX_EPOCH)
                    .unwrap()
                    .as_secs() as i64
            ));

        self.set_activity(test_activity).await?;
        
        // Wait a moment then clear
        sleep(Duration::from_secs(2)).await;
        self.clear_activity().await?;
        
        self.logger.info("Discord IPC connection test completed successfully");
        Ok(())
    }
}

impl Drop for DiscordIpcService {
    fn drop(&mut self) {
        self.disconnect();
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio_test;

    #[tokio::test]
    async fn test_discord_service_creation() {
        let service = DiscordIpcService::new(None);
        assert!(!service.is_connected());
    }

    #[tokio::test]
    async fn test_discord_service_with_pipe_number() {
        let service = DiscordIpcService::new(Some(0));
        assert!(!service.is_connected());
    }

    // Note: Actual connection tests would require Discord to be running
    // and would be integration tests rather than unit tests
}

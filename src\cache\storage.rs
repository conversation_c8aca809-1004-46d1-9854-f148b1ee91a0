//! Cache storage implementation

use crate::constants;
use crate::error::{CacheError, Result};
use crate::utils::ensure_directory_exists;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fs;
use std::sync::{Arc, RwLock};
use std::time::{SystemTime, UNIX_EPOCH};

/// Cache entry with value and expiration time
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheEntry {
    pub value: String,
    pub expiry: u64, // 0 means no expiration
}

impl CacheEntry {
    /// Create a new cache entry
    pub fn new(value: String, expiry: u64) -> Self {
        Self { value, expiry }
    }

    /// Check if the entry is expired
    pub fn is_expired(&self) -> bool {
        if self.expiry == 0 {
            false
        } else {
            let now = SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs();
            now > self.expiry
        }
    }
}

/// Thread-safe cache storage
#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct CacheStorage {
    data: Arc<RwLock<HashMap<String, CacheEntry>>>,
    file_path: String,
}

impl CacheStorage {
    /// Create a new cache storage instance
    pub fn new() -> Result<Self> {
        ensure_directory_exists(constants::DATA_DIRECTORY)?;
        
        let cache = Self {
            data: Arc::new(RwLock::new(HashMap::new())),
            file_path: constants::CACHE_FILE_PATH.to_string(),
        };

        // Load existing cache from file
        cache.load_from_file()?;
        
        Ok(cache)
    }

    /// Get a value from the cache
    pub fn get(&self, key: &str) -> Option<String> {
        let data = self.data.read().ok()?;
        
        if let Some(entry) = data.get(key) {
            if entry.is_expired() {
                // Entry is expired, remove it
                drop(data);
                self.remove(key);
                None
            } else {
                Some(entry.value.clone())
            }
        } else {
            None
        }
    }

    /// Set a value in the cache
    pub fn set(&self, key: String, value: String, expiry: u64) -> Result<()> {
        let entry = CacheEntry::new(value, expiry);
        
        {
            let mut data = self.data.write()
                .map_err(|_| CacheError::WriteFailed("Failed to acquire write lock".to_string()))?;
            data.insert(key, entry);
        }

        // Save to file
        self.save_to_file()?;
        Ok(())
    }

    /// Remove a value from the cache
    pub fn remove(&self, key: &str) -> Option<String> {
        let mut data = self.data.write().ok()?;
        let removed = data.remove(key).map(|entry| entry.value);
        
        if removed.is_some() {
            drop(data);
            let _ = self.save_to_file();
        }
        
        removed
    }

    /// Clear all entries from the cache
    pub fn clear(&self) -> Result<()> {
        {
            let mut data = self.data.write()
                .map_err(|_| CacheError::WriteFailed("Failed to acquire write lock".to_string()))?;
            data.clear();
        }

        self.save_to_file()?;
        Ok(())
    }

    /// Clean up expired entries
    pub fn cleanup_expired(&self) -> Result<usize> {
        let mut removed_count = 0;
        
        {
            let mut data = self.data.write()
                .map_err(|_| CacheError::WriteFailed("Failed to acquire write lock".to_string()))?;
            
            let expired_keys: Vec<String> = data
                .iter()
                .filter(|(_, entry)| entry.is_expired())
                .map(|(key, _)| key.clone())
                .collect();
            
            for key in expired_keys {
                data.remove(&key);
                removed_count += 1;
            }
        }

        if removed_count > 0 {
            self.save_to_file()?;
            log::debug!("Cleaned up {} expired cache entries", removed_count);
        }

        Ok(removed_count)
    }

    /// Get the number of entries in the cache
    pub fn len(&self) -> usize {
        self.data.read().map(|data| data.len()).unwrap_or(0)
    }

    /// Check if the cache is empty
    pub fn is_empty(&self) -> bool {
        self.len() == 0
    }

    /// Get all keys in the cache
    pub fn keys(&self) -> Vec<String> {
        self.data
            .read()
            .map(|data| data.keys().cloned().collect())
            .unwrap_or_default()
    }

    /// Load cache from file
    fn load_from_file(&self) -> Result<()> {
        if !std::path::Path::new(&self.file_path).exists() {
            log::debug!("Cache file does not exist, starting with empty cache");
            return Ok(());
        }

        let content = fs::read_to_string(&self.file_path)
            .map_err(|e| CacheError::Corrupted(format!("Failed to read cache file: {}", e)))?;

        if content.trim().is_empty() {
            log::debug!("Cache file is empty, starting with empty cache");
            return Ok(());
        }

        let loaded_data: HashMap<String, CacheEntry> = serde_json::from_str(&content)
            .map_err(|e| CacheError::Corrupted(format!("Failed to parse cache file: {}", e)))?;

        {
            let mut data = self.data.write()
                .map_err(|_| CacheError::WriteFailed("Failed to acquire write lock".to_string()))?;
            *data = loaded_data;
        }

        log::debug!("Cache loaded from file with {} entries", self.len());
        
        // Clean up expired entries after loading
        self.cleanup_expired()?;
        
        Ok(())
    }

    /// Save cache to file
    fn save_to_file(&self) -> Result<()> {
        let data = self.data.read()
            .map_err(|_| CacheError::WriteFailed("Failed to acquire read lock".to_string()))?;

        let content = serde_json::to_string_pretty(&*data)
            .map_err(|e| CacheError::WriteFailed(format!("Failed to serialize cache: {}", e)))?;

        fs::write(&self.file_path, content)
            .map_err(|e| CacheError::WriteFailed(format!("Failed to write cache file: {}", e)))?;

        log::debug!("Cache saved to file with {} entries", data.len());
        Ok(())
    }
}

impl Default for CacheStorage {
    fn default() -> Self {
        Self::new().expect("Failed to create cache storage")
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;
    use std::time::Duration;

    fn create_test_cache() -> (CacheStorage, TempDir) {
        let temp_dir = TempDir::new().unwrap();
        let cache_path = temp_dir.path().join("test_cache.json");
        
        let cache = CacheStorage {
            data: Arc::new(RwLock::new(HashMap::new())),
            file_path: cache_path.to_string_lossy().to_string(),
        };
        
        (cache, temp_dir)
    }

    #[test]
    fn test_cache_basic_operations() {
        let (cache, _temp_dir) = create_test_cache();

        // Test set and get
        cache.set("key1".to_string(), "value1".to_string(), 0).unwrap();
        assert_eq!(cache.get("key1"), Some("value1".to_string()));

        // Test non-existent key
        assert_eq!(cache.get("nonexistent"), None);

        // Test remove
        assert_eq!(cache.remove("key1"), Some("value1".to_string()));
        assert_eq!(cache.get("key1"), None);
    }

    #[test]
    fn test_cache_expiration() {
        let (cache, _temp_dir) = create_test_cache();

        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();

        // Set entry that expires in 1 second
        cache.set("expiring".to_string(), "value".to_string(), now + 1).unwrap();
        assert_eq!(cache.get("expiring"), Some("value".to_string()));

        // Set entry that's already expired
        cache.set("expired".to_string(), "value".to_string(), now - 1).unwrap();
        assert_eq!(cache.get("expired"), None);
    }

    #[test]
    fn test_cache_cleanup() {
        let (cache, _temp_dir) = create_test_cache();

        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();

        // Add some entries
        cache.set("permanent".to_string(), "value".to_string(), 0).unwrap();
        cache.set("expired1".to_string(), "value".to_string(), now - 1).unwrap();
        cache.set("expired2".to_string(), "value".to_string(), now - 2).unwrap();

        assert_eq!(cache.len(), 3);
        
        let removed = cache.cleanup_expired().unwrap();
        assert_eq!(removed, 2);
        assert_eq!(cache.len(), 1);
        assert_eq!(cache.get("permanent"), Some("value".to_string()));
    }
}

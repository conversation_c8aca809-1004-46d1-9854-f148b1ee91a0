name: Release
run-name: Release ${{ github.ref_name }}
on:
  push:
    tags:
      - v*
  workflow_dispatch:
jobs:
  release:
    runs-on: ubuntu-latest
    permissions:
      contents: write
      packages: write
      id-token: write
      attestations: write
    env:
      RELEASE_ZIP_FILENAME: ${{ github.event.repository.name }}-${{ github.ref_name }}.zip
      DOCKER_IMAGE_NAME: ghcr.io/${{ github.actor }}/${{ github.event.repository.name }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"
          cache: pip
      - name: Install Python packages
        run: pip install -U -r requirements.txt pyright
      - name: Lint
        run: make lint
      - name: Sign into GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Build and push Docker images
        id: docker-build-push-action
        uses: docker/build-push-action@v6
        with:
          context: .
          platforms: linux/amd64,linux/arm64,linux/386,linux/arm/v7
          push: true
          provenance: false
          tags: ${{ env.DOCKER_IMAGE_NAME }}:${{ github.ref_name }},${{ env.DOCKER_IMAGE_NAME }}:latest
      - name: Generate signed build provenance attestation
        uses: actions/attest-build-provenance@v2
        with:
          subject-name: ${{ env.DOCKER_IMAGE_NAME }}
          subject-digest: ${{ steps.docker-build-push-action.outputs.digest }}
      - name: Create release ZIP file
        run: |
          mv .github/release-notes ../
          xargs rm -rf < .dockerignore
          mkdir ${{ github.event.repository.name }}
          mv * ${{ github.event.repository.name }} || true
          zip -r ${{ env.RELEASE_ZIP_FILENAME }} ${{ github.event.repository.name }}
      - name: Release on GitHub
        uses: softprops/action-gh-release@da05d552573ad5aba039eaac05058a918a7bf631
        with:
          tag_name: ${{ github.ref_name }}
          draft: false
          body_path: ../release-notes/${{ github.ref_name }}.md
          files: ${{ env.RELEASE_ZIP_FILENAME }}
      - name: Generate signed build provenance attestation
        uses: actions/attest-build-provenance@v2
        with:
          subject-path: ${{ env.RELEASE_ZIP_FILENAME }}

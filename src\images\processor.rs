//! Image processing functionality

use crate::config::PostersConfig;
use crate::error::{ImageError, Result};
use image::{DynamicImage, ImageFormat, RgbaImage};
use std::io::Cursor;

/// Image processor for handling poster images
pub struct ImageProcessor {
    config: PostersConfig,
}

impl ImageProcessor {
    /// Create a new image processor
    pub fn new(config: PostersConfig) -> Self {
        Self { config }
    }

    /// Process an image from bytes
    pub fn process_image(&self, image_data: &[u8]) -> Result<Vec<u8>> {
        // Load the image
        let original_image = self.load_image(image_data)?;
        
        // Process the image
        let processed_image = self.resize_and_fit(original_image)?;
        
        // Convert to PNG bytes
        self.to_png_bytes(processed_image)
    }

    /// Load image from bytes
    fn load_image(&self, data: &[u8]) -> Result<DynamicImage> {
        let cursor = Cursor::new(data);
        
        image::load(cursor, ImageFormat::from_extension("png").unwrap_or(ImageFormat::Png))
            .or_else(|_| {
                let cursor = Cursor::new(data);
                image::load(cursor, ImageFormat::Jpeg)
            })
            .or_else(|_| {
                // Try to guess the format
                let cursor = Cursor::new(data);
                image::load_from_memory(data)
            })
            .map_err(|e| ImageError::LoadFailed(format!("Failed to load image: {}", e)).into())
    }

    /// Resize and fit the image according to configuration
    fn resize_and_fit(&self, mut image: DynamicImage) -> Result<DynamicImage> {
        let (width, height) = image.dimensions();
        
        // Check if resizing is needed
        let max_size = self.config.max_size;
        if max_size > 0 && (width > max_size || height > max_size) {
            // Calculate new dimensions while preserving aspect ratio
            let scale = (max_size as f32) / width.max(height) as f32;
            let new_width = (width as f32 * scale) as u32;
            let new_height = (height as f32 * scale) as u32;
            
            image = image.resize(
                new_width,
                new_height,
                image::imageops::FilterType::Lanczos3,
            );
            
            log::debug!("Resized image from {}x{} to {}x{}", width, height, new_width, new_height);
        }

        // Fit to square if configured
        if self.config.fit {
            image = self.fit_to_square(image)?;
        }

        Ok(image)
    }

    /// Fit image to a square while maintaining aspect ratio
    fn fit_to_square(&self, image: DynamicImage) -> Result<DynamicImage> {
        let (width, height) = image.dimensions();
        
        if width == height {
            return Ok(image); // Already square
        }

        let size = width.max(height);
        let mut square_image = RgbaImage::new(size, size);
        
        // Fill with transparent pixels
        for pixel in square_image.pixels_mut() {
            *pixel = image::Rgba([0, 0, 0, 0]);
        }

        // Calculate position to center the image
        let x_offset = (size - width) / 2;
        let y_offset = (size - height) / 2;

        // Overlay the original image
        image::imageops::overlay(&mut square_image, &image.to_rgba8(), x_offset as i64, y_offset as i64);

        log::debug!("Fitted image to square {}x{}", size, size);
        Ok(DynamicImage::ImageRgba8(square_image))
    }

    /// Convert image to PNG bytes
    fn to_png_bytes(&self, image: DynamicImage) -> Result<Vec<u8>> {
        let mut buffer = Vec::new();
        let mut cursor = Cursor::new(&mut buffer);
        
        image.write_to(&mut cursor, ImageFormat::Png)
            .map_err(|e| ImageError::ProcessingFailed(format!("Failed to encode PNG: {}", e)))?;

        log::debug!("Converted image to PNG, size: {} bytes", buffer.len());
        Ok(buffer)
    }

    /// Validate image size
    pub fn validate_image_size(&self, data: &[u8]) -> Result<()> {
        const MAX_IMAGE_SIZE: usize = 10 * 1024 * 1024; // 10MB
        
        if data.len() > MAX_IMAGE_SIZE {
            return Err(ImageError::TooLarge { size: data.len() }.into());
        }
        
        Ok(())
    }

    /// Get image dimensions without fully loading it
    pub fn get_image_dimensions(&self, data: &[u8]) -> Result<(u32, u32)> {
        let cursor = Cursor::new(data);
        let reader = image::io::Reader::new(cursor).with_guessed_format()
            .map_err(|e| ImageError::InvalidFormat(format!("Failed to detect format: {}", e)))?;
        
        let dimensions = reader.into_dimensions()
            .map_err(|e| ImageError::LoadFailed(format!("Failed to read dimensions: {}", e)))?;
        
        Ok(dimensions)
    }

    /// Check if the image format is supported
    pub fn is_supported_format(&self, data: &[u8]) -> bool {
        // Try to detect the format
        let cursor = Cursor::new(data);
        if let Ok(reader) = image::io::Reader::new(cursor).with_guessed_format() {
            if let Some(format) = reader.format() {
                matches!(format, ImageFormat::Png | ImageFormat::Jpeg | ImageFormat::WebP | ImageFormat::Gif)
            } else {
                false
            }
        } else {
            false
        }
    }

    /// Create a thumbnail version of the image
    pub fn create_thumbnail(&self, image_data: &[u8], size: u32) -> Result<Vec<u8>> {
        let image = self.load_image(image_data)?;
        
        let thumbnail = image.thumbnail(size, size);
        
        self.to_png_bytes(thumbnail)
    }

    /// Optimize image for Discord (reduce file size while maintaining quality)
    pub fn optimize_for_discord(&self, image_data: &[u8]) -> Result<Vec<u8>> {
        let image = self.load_image(image_data)?;
        
        // Discord prefers smaller images, so we might want to be more aggressive with compression
        let processed = self.resize_and_fit(image)?;
        
        // Convert to PNG with optimization
        let mut buffer = Vec::new();
        let mut cursor = Cursor::new(&mut buffer);
        
        // For PNG, we can't control compression much, but we ensure it's optimized
        processed.write_to(&mut cursor, ImageFormat::Png)
            .map_err(|e| ImageError::ProcessingFailed(format!("Failed to optimize image: {}", e)))?;

        // If the result is still too large, try reducing quality further
        if buffer.len() > 256 * 1024 { // 256KB threshold
            log::debug!("Image still large after processing, applying additional optimization");
            
            // Reduce size more aggressively
            let smaller_size = (self.config.max_size as f32 * 0.8) as u32;
            let smaller_image = processed.resize(
                smaller_size,
                smaller_size,
                image::imageops::FilterType::Lanczos3,
            );
            
            buffer.clear();
            cursor = Cursor::new(&mut buffer);
            smaller_image.write_to(&mut cursor, ImageFormat::Png)
                .map_err(|e| ImageError::ProcessingFailed(format!("Failed to optimize image: {}", e)))?;
        }

        log::debug!("Optimized image for Discord, final size: {} bytes", buffer.len());
        Ok(buffer)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    fn create_test_config() -> PostersConfig {
        PostersConfig {
            enabled: true,
            imgur_client_id: String::new(),
            max_size: 256,
            fit: true,
        }
    }

    fn create_test_image() -> Vec<u8> {
        // Create a simple 100x100 red square PNG
        let image = RgbaImage::from_fn(100, 100, |_, _| image::Rgba([255, 0, 0, 255]));
        let dynamic_image = DynamicImage::ImageRgba8(image);
        
        let mut buffer = Vec::new();
        let mut cursor = Cursor::new(&mut buffer);
        dynamic_image.write_to(&mut cursor, ImageFormat::Png).unwrap();
        buffer
    }

    #[test]
    fn test_image_processor_creation() {
        let config = create_test_config();
        let processor = ImageProcessor::new(config);
        assert_eq!(processor.config.max_size, 256);
    }

    #[test]
    fn test_image_processing() {
        let config = create_test_config();
        let processor = ImageProcessor::new(config);
        let test_image = create_test_image();
        
        let result = processor.process_image(&test_image);
        assert!(result.is_ok());
        
        let processed = result.unwrap();
        assert!(!processed.is_empty());
    }

    #[test]
    fn test_image_validation() {
        let config = create_test_config();
        let processor = ImageProcessor::new(config);
        let test_image = create_test_image();
        
        assert!(processor.validate_image_size(&test_image).is_ok());
        
        // Test with oversized data
        let large_data = vec![0u8; 20 * 1024 * 1024]; // 20MB
        assert!(processor.validate_image_size(&large_data).is_err());
    }

    #[test]
    fn test_format_detection() {
        let config = create_test_config();
        let processor = ImageProcessor::new(config);
        let test_image = create_test_image();
        
        assert!(processor.is_supported_format(&test_image));
        
        // Test with invalid data
        let invalid_data = vec![0u8; 100];
        assert!(!processor.is_supported_format(&invalid_data));
    }
}

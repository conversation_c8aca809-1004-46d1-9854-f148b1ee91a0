//! Configuration file loading and saving

use crate::config::types::*;
use crate::constants;
use crate::error::{Config<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Result};
use crate::utils::ensure_directory_exists;
use std::fs;
use std::path::Path;

/// Supported configuration file formats
#[derive(Debug, <PERSON>lone, Copy)]
pub enum ConfigFormat {
    Yaml,
    Json,
}

impl ConfigFormat {
    fn extension(&self) -> &'static str {
        match self {
            ConfigFormat::Yaml => "yaml",
            ConfigFormat::Json => "json",
        }
    }

    fn from_extension(ext: &str) -> Option<Self> {
        match ext.to_lowercase().as_str() {
            "yaml" | "yml" => Some(ConfigFormat::Yaml),
            "json" => Some(ConfigFormat::Json),
            _ => None,
        }
    }
}

/// Configuration manager
pub struct ConfigManager {
    config: Config,
    file_path: String,
    format: ConfigFormat,
}

impl ConfigManager {
    /// Create a new configuration manager
    pub fn new() -> Result<Self> {
        ensure_directory_exists(constants::DATA_DIRECTORY)?;
        
        let (file_path, format) = Self::find_config_file()?;
        let config = if Path::new(&file_path).exists() {
            Self::load_from_file(&file_path, format)?
        } else {
            Config::default()
        };

        let mut manager = Self {
            config,
            file_path,
            format,
        };

        // Migrate old config format if needed
        manager.migrate_config()?;
        
        // Validate configuration
        manager.config.validate()?;
        
        // Save to ensure file exists with current format
        manager.save()?;

        Ok(manager)
    }

    /// Get the current configuration
    pub fn config(&self) -> &Config {
        &self.config
    }

    /// Get a mutable reference to the configuration
    pub fn config_mut(&mut self) -> &mut Config {
        &mut self.config
    }

    /// Reload configuration from file
    pub fn reload(&mut self) -> Result<()> {
        if Path::new(&self.file_path).exists() {
            self.config = Self::load_from_file(&self.file_path, self.format)?;
            self.config.validate()?;
            log::info!("Configuration reloaded from file");
        } else {
            log::warn!("Configuration file not found, using defaults");
            self.config = Config::default();
        }
        Ok(())
    }

    /// Save configuration to file
    pub fn save(&self) -> Result<()> {
        let content = match self.format {
            ConfigFormat::Yaml => serde_yaml_bw::to_string(&self.config)?,
            ConfigFormat::Json => serde_json::to_string_pretty(&self.config)?,
        };

        fs::write(&self.file_path, content)?;
        log::debug!("Configuration saved to {}", self.file_path);
        Ok(())
    }

    /// Find the configuration file, preferring YAML over JSON
    fn find_config_file() -> Result<(String, ConfigFormat)> {
        let base_path = constants::CONFIG_FILE_BASE;
        
        // Try YAML first
        for ext in &["yaml", "yml"] {
            let path = format!("{}.{}", base_path, ext);
            if Path::new(&path).exists() {
                return Ok((path, ConfigFormat::Yaml));
            }
        }

        // Try JSON
        let json_path = format!("{}.json", base_path);
        if Path::new(&json_path).exists() {
            return Ok((json_path, ConfigFormat::Json));
        }

        // Default to YAML if no file exists
        Ok((format!("{}.yaml", base_path), ConfigFormat::Yaml))
    }

    /// Load configuration from file
    fn load_from_file(file_path: &str, format: ConfigFormat) -> Result<Config> {
        let content = fs::read_to_string(file_path)
            .map_err(|e| ConfigError::FileNotFound(format!("{}: {}", file_path, e)))?;

        let config: Config = match format {
            ConfigFormat::Yaml => serde_yaml_bw::from_str(&content)
                .map_err(|e| ConfigError::InvalidFormat(format!("YAML parse error: {}", e)))?,
            ConfigFormat::Json => serde_json::from_str(&content)
                .map_err(|e| ConfigError::InvalidFormat(format!("JSON parse error: {}", e)))?,
        };

        log::debug!("Configuration loaded from {}", file_path);
        Ok(config)
    }

    /// Migrate old configuration format
    fn migrate_config(&mut self) -> Result<()> {
        let mut changed = false;

        // Handle legacy field names and values
        // This would be expanded based on actual migration needs
        
        if changed {
            log::info!("Configuration migrated to current format");
            self.save()?;
        }

        Ok(())
    }
}

impl Default for ConfigManager {
    fn default() -> Self {
        Self::new().expect("Failed to create configuration manager")
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;

    #[test]
    fn test_config_format_detection() {
        assert_eq!(ConfigFormat::from_extension("yaml"), Some(ConfigFormat::Yaml));
        assert_eq!(ConfigFormat::from_extension("yml"), Some(ConfigFormat::Yaml));
        assert_eq!(ConfigFormat::from_extension("json"), Some(ConfigFormat::Json));
        assert_eq!(ConfigFormat::from_extension("txt"), None);
    }

    #[test]
    fn test_default_config_validation() {
        let config = Config::default();
        assert!(config.validate().is_ok());
    }

    #[test]
    fn test_config_serialization() {
        let config = Config::default();
        
        // Test YAML serialization
        let yaml = serde_yaml_bw::to_string(&config).unwrap();
        let deserialized: Config = serde_yaml_bw::from_str(&yaml).unwrap();
        assert_eq!(config.logging.debug, deserialized.logging.debug);
        
        // Test JSON serialization
        let json = serde_json::to_string(&config).unwrap();
        let deserialized: Config = serde_json::from_str(&json).unwrap();
        assert_eq!(config.display.duration, deserialized.display.duration);
    }
}

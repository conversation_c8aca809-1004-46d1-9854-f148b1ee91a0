//! Error types for the Discord Rich Presence for Plex application

use thiserror::Error;

/// Main error type for the application
#[derive(Error, Debug)]
pub enum Error {
    #[error("Configuration error: {0}")]
    Config(#[from] ConfigError),

    #[error("Discord error: {0}")]
    Discord(#[from] DiscordError),

    #[error("Plex error: {0}")]
    Plex(#[from] PlexError),

    #[error("Image processing error: {0}")]
    Image(#[from] ImageError),

    #[error("Cache error: {0}")]
    Cache(#[from] CacheError),

    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),

    #[error("HTTP error: {0}")]
    Http(#[from] reqwest::Error),

    #[error("JSON error: {0}")]
    J<PERSON>(#[from] serde_json::Error),

    #[error("YAML error: {0}")]
    Yaml(#[from] serde_yaml_bw::Error),

    #[error("WebSocket error: {0}")]
    WebSocket(#[from] tokio_tungstenite::tungstenite::Error),

    #[error("URL parse error: {0}")]
    UrlParse(#[from] url::ParseError),

    #[error("UUID error: {0}")]
    Uuid(#[from] uuid::Error),

    #[error("Other error: {0}")]
    Other(String),
}

/// Configuration-related errors
#[derive(Error, Debug)]
pub enum ConfigError {
    #[error("Config file not found: {0}")]
    FileNotFound(String),

    #[error("Invalid config format: {0}")]
    InvalidFormat(String),

    #[error("Missing required field: {0}")]
    MissingField(String),

    #[error("Invalid value for field '{field}': {value}")]
    InvalidValue { field: String, value: String },

    #[error("Config validation failed: {0}")]
    ValidationFailed(String),
}

/// Discord-related errors
#[derive(Error, Debug)]
pub enum DiscordError {
    #[error("Failed to connect to Discord IPC")]
    ConnectionFailed,

    #[error("Discord IPC pipe not found")]
    PipeNotFound,

    #[error("Failed to set activity: {0}")]
    ActivityFailed(String),

    #[error("Discord client not connected")]
    NotConnected,

    #[error("Invalid activity data: {0}")]
    InvalidActivity(String),
}

/// Plex-related errors
#[derive(Error, Debug)]
pub enum PlexError {
    #[error("Authentication failed: {0}")]
    AuthenticationFailed(String),

    #[error("Server not found: {name}")]
    ServerNotFound { name: String },

    #[error("Connection to server failed: {0}")]
    ConnectionFailed(String),

    #[error("API request failed: {0}")]
    ApiRequestFailed(String),

    #[error("Invalid response format: {0}")]
    InvalidResponse(String),

    #[error("WebSocket connection failed: {0}")]
    WebSocketFailed(String),

    #[error("Alert parsing failed: {0}")]
    AlertParsingFailed(String),

    #[error("Media not found: {rating_key}")]
    MediaNotFound { rating_key: i64 },

    #[error("Unsupported media type: {media_type}")]
    UnsupportedMediaType { media_type: String },
}

/// Image processing errors
#[derive(Error, Debug)]
pub enum ImageError {
    #[error("Failed to load image: {0}")]
    LoadFailed(String),

    #[error("Failed to process image: {0}")]
    ProcessingFailed(String),

    #[error("Failed to upload image: {0}")]
    UploadFailed(String),

    #[error("Invalid image format: {0}")]
    InvalidFormat(String),

    #[error("Image too large: {size} bytes")]
    TooLarge { size: usize },
}

/// Cache-related errors
#[derive(Error, Debug)]
pub enum CacheError {
    #[error("Cache file corrupted: {0}")]
    Corrupted(String),

    #[error("Cache entry not found: {key}")]
    EntryNotFound { key: String },

    #[error("Cache entry expired: {key}")]
    EntryExpired { key: String },

    #[error("Failed to write cache: {0}")]
    WriteFailed(String),
}

/// Result type alias for convenience
pub type Result<T> = std::result::Result<T, Error>;

impl From<String> for Error {
    fn from(s: String) -> Self {
        Error::Other(s)
    }
}

impl From<&str> for Error {
    fn from(s: &str) -> Self {
        Error::Other(s.to_string())
    }
}

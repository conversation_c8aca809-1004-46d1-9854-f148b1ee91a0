//! Configuration type definitions

use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// Main configuration structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Config {
    pub logging: LoggingConfig,
    pub display: DisplayConfig,
    pub users: Vec<UserConfig>,
}

/// Logging configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoggingConfig {
    #[serde(default = "default_debug")]
    pub debug: bool,
    #[serde(default = "default_write_to_file")]
    pub write_to_file: bool,
}

/// Display configuration for Rich Presence
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DisplayConfig {
    #[serde(default = "default_duration")]
    pub duration: bool,
    #[serde(default = "default_genres")]
    pub genres: bool,
    #[serde(default = "default_album")]
    pub album: bool,
    #[serde(default = "default_album_image")]
    pub album_image: bool,
    #[serde(default = "default_artist")]
    pub artist: bool,
    #[serde(default = "default_artist_image")]
    pub artist_image: bool,
    #[serde(default = "default_year")]
    pub year: bool,
    #[serde(default = "default_status_icon")]
    pub status_icon: bool,
    #[serde(default = "default_progress_mode")]
    pub progress_mode: ProgressMode,
    #[serde(default = "default_paused")]
    pub paused: bool,
    #[serde(default)]
    pub posters: PostersConfig,
    #[serde(default)]
    pub buttons: Vec<ButtonConfig>,
}

/// Progress display mode
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum ProgressMode {
    Off,
    Elapsed,
    Remaining,
    Bar,
}

/// Poster configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PostersConfig {
    #[serde(default = "default_posters_enabled")]
    pub enabled: bool,
    #[serde(default)]
    pub imgur_client_id: String,
    #[serde(default = "default_max_size")]
    pub max_size: u32,
    #[serde(default = "default_fit")]
    pub fit: bool,
}

/// Button configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ButtonConfig {
    pub label: String,
    pub url: String,
    #[serde(default)]
    pub media_types: Vec<String>,
}

/// User configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserConfig {
    pub token: String,
    pub servers: Vec<ServerConfig>,
}

/// Server configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerConfig {
    pub name: String,
    #[serde(default)]
    pub listen_for_user: Option<String>,
    #[serde(default)]
    pub blacklisted_libraries: Vec<String>,
    #[serde(default)]
    pub whitelisted_libraries: Vec<String>,
    #[serde(default)]
    pub ipc_pipe_number: Option<i32>,
}

// Default value functions
fn default_debug() -> bool { true }
fn default_write_to_file() -> bool { false }
fn default_duration() -> bool { false }
fn default_genres() -> bool { true }
fn default_album() -> bool { true }
fn default_album_image() -> bool { true }
fn default_artist() -> bool { true }
fn default_artist_image() -> bool { true }
fn default_year() -> bool { true }
fn default_status_icon() -> bool { false }
fn default_progress_mode() -> ProgressMode { ProgressMode::Bar }
fn default_paused() -> bool { false }
fn default_posters_enabled() -> bool { true }
fn default_max_size() -> u32 { 256 }
fn default_fit() -> bool { true }

impl Default for Config {
    fn default() -> Self {
        Self {
            logging: LoggingConfig::default(),
            display: DisplayConfig::default(),
            users: Vec::new(),
        }
    }
}

impl Default for LoggingConfig {
    fn default() -> Self {
        Self {
            debug: default_debug(),
            write_to_file: default_write_to_file(),
        }
    }
}

impl Default for DisplayConfig {
    fn default() -> Self {
        Self {
            duration: default_duration(),
            genres: default_genres(),
            album: default_album(),
            album_image: default_album_image(),
            artist: default_artist(),
            artist_image: default_artist_image(),
            year: default_year(),
            status_icon: default_status_icon(),
            progress_mode: default_progress_mode(),
            paused: default_paused(),
            posters: PostersConfig::default(),
            buttons: Vec::new(),
        }
    }
}

impl Default for PostersConfig {
    fn default() -> Self {
        Self {
            enabled: default_posters_enabled(),
            imgur_client_id: String::new(),
            max_size: default_max_size(),
            fit: default_fit(),
        }
    }
}

impl Default for ProgressMode {
    fn default() -> Self {
        ProgressMode::Bar
    }
}

impl Config {
    /// Validate the configuration
    pub fn validate(&self) -> crate::Result<()> {
        // Validate progress mode
        if !matches!(self.display.progress_mode, 
            ProgressMode::Off | ProgressMode::Elapsed | ProgressMode::Remaining | ProgressMode::Bar) {
            return Err(crate::error::ConfigError::InvalidValue {
                field: "display.progress_mode".to_string(),
                value: format!("{:?}", self.display.progress_mode),
            }.into());
        }

        // Validate poster max size
        if self.display.posters.max_size == 0 || self.display.posters.max_size > 2048 {
            return Err(crate::error::ConfigError::InvalidValue {
                field: "display.posters.max_size".to_string(),
                value: self.display.posters.max_size.to_string(),
            }.into());
        }

        // Validate users
        if self.users.is_empty() {
            log::warn!("No users configured");
        }

        for user in &self.users {
            if user.token.is_empty() {
                return Err(crate::error::ConfigError::MissingField("user.token".to_string()).into());
            }
            
            if user.servers.is_empty() {
                return Err(crate::error::ConfigError::MissingField("user.servers".to_string()).into());
            }

            for server in &user.servers {
                if server.name.is_empty() {
                    return Err(crate::error::ConfigError::MissingField("server.name".to_string()).into());
                }
            }
        }

        Ok(())
    }
}

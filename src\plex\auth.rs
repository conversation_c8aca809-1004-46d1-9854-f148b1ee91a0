//! Plex authentication implementation

use crate::constants;
use crate::error::{PlexError, Result};
use reqwest::Client;
use serde::{Deserialize, Serialize};
use std::time::Duration;
use tokio::time::sleep;
use url::Url;

/// Plex PIN response for authentication
#[derive(Debug, Deserialize)]
struct PlexPinResponse {
    id: String,
    code: String,
}

/// Plex PIN status response
#[derive(Debug, Deserialize)]
struct PlexPinStatusResponse {
    #[serde(rename = "authToken")]
    auth_token: Option<String>,
}

/// Plex authentication service
pub struct PlexAuth {
    client: Client,
}

impl PlexAuth {
    /// Create a new Plex authentication service
    pub fn new() -> Result<Self> {
        let client = Client::builder()
            .timeout(Duration::from_secs(30))
            .build()
            .map_err(|e| PlexError::AuthenticationFailed(format!("Failed to create HTTP client: {}", e)))?;

        Ok(Self { client })
    }

    /// Initiate the authentication process
    pub async fn initiate_auth(&self) -> Result<(String, String, String)> {
        log::info!("Initiating Plex authentication");

        let response = self.client
            .post("https://plex.tv/api/v2/pins.json?strong=true")
            .header("X-Plex-Product", constants::NAME)
            .header("X-Plex-Client-Identifier", constants::PLEX_CLIENT_ID)
            .send()
            .await
            .map_err(|e| PlexError::AuthenticationFailed(format!("Failed to request PIN: {}", e)))?;

        if !response.status().is_success() {
            return Err(PlexError::AuthenticationFailed(format!(
                "PIN request failed with status: {}",
                response.status()
            )).into());
        }

        let pin_response: PlexPinResponse = response
            .json()
            .await
            .map_err(|e| PlexError::AuthenticationFailed(format!("Failed to parse PIN response: {}", e)))?;

        // Build the authentication URL
        let auth_url = self.build_auth_url(&pin_response.code)?;

        log::debug!("Generated PIN: {}, Code: {}", pin_response.id, pin_response.code);
        Ok((pin_response.id, pin_response.code, auth_url))
    }

    /// Check if authentication is complete and get the token
    pub async fn get_auth_token(&self, id: &str, code: &str) -> Result<Option<String>> {
        let url = format!("https://plex.tv/api/v2/pins/{}.json?code={}", id, code);
        
        let response = self.client
            .get(&url)
            .header("X-Plex-Client-Identifier", constants::PLEX_CLIENT_ID)
            .send()
            .await
            .map_err(|e| PlexError::AuthenticationFailed(format!("Failed to check PIN status: {}", e)))?;

        if !response.status().is_success() {
            return Err(PlexError::AuthenticationFailed(format!(
                "PIN status check failed with status: {}",
                response.status()
            )).into());
        }

        let status_response: PlexPinStatusResponse = response
            .json()
            .await
            .map_err(|e| PlexError::AuthenticationFailed(format!("Failed to parse PIN status: {}", e)))?;

        Ok(status_response.auth_token)
    }

    /// Complete authentication flow with user interaction
    pub async fn authenticate_user(&self) -> Result<String> {
        let (id, code, auth_url) = self.initiate_auth().await?;

        log::info!("Open the below URL in your web browser and sign in:");
        log::info!("{}", auth_url);

        // Wait a moment for user to see the message
        sleep(Duration::from_secs(5)).await;

        // Poll for authentication completion
        for attempt in 1..=35 {
            log::info!("Checking whether authentication is successful ({}s)", attempt * 5);
            
            if let Some(token) = self.get_auth_token(&id, &code).await? {
                log::info!("Authentication successful");
                return Ok(token);
            }

            sleep(Duration::from_secs(5)).await;
        }

        Err(PlexError::AuthenticationFailed("Authentication timed out (180s)".to_string()).into())
    }

    /// Validate an existing token
    pub async fn validate_token(&self, token: &str) -> Result<bool> {
        let response = self.client
            .get("https://plex.tv/api/v2/user")
            .header("X-Plex-Token", token)
            .header("X-Plex-Client-Identifier", constants::PLEX_CLIENT_ID)
            .send()
            .await
            .map_err(|e| PlexError::AuthenticationFailed(format!("Failed to validate token: {}", e)))?;

        Ok(response.status().is_success())
    }

    /// Get user information from token
    pub async fn get_user_info(&self, token: &str) -> Result<PlexUser> {
        let response = self.client
            .get("https://plex.tv/api/v2/user")
            .header("X-Plex-Token", token)
            .header("X-Plex-Client-Identifier", constants::PLEX_CLIENT_ID)
            .send()
            .await
            .map_err(|e| PlexError::AuthenticationFailed(format!("Failed to get user info: {}", e)))?;

        if !response.status().is_success() {
            return Err(PlexError::AuthenticationFailed(format!(
                "Failed to get user info: HTTP {}",
                response.status()
            )).into());
        }

        let user: PlexUser = response
            .json()
            .await
            .map_err(|e| PlexError::AuthenticationFailed(format!("Failed to parse user info: {}", e)))?;

        Ok(user)
    }

    /// Build the authentication URL
    fn build_auth_url(&self, code: &str) -> Result<String> {
        let mut url = Url::parse("https://app.plex.tv/auth")
            .map_err(|e| PlexError::AuthenticationFailed(format!("Failed to parse base URL: {}", e)))?;

        // Add query parameters
        url.query_pairs_mut()
            .append_pair("clientID", constants::PLEX_CLIENT_ID)
            .append_pair("code", code)
            .append_pair("context[device][product]", constants::NAME);

        Ok(url.to_string())
    }
}

/// Plex user information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PlexUser {
    pub id: u64,
    pub username: String,
    pub email: String,
    pub title: String,
    #[serde(rename = "thumb")]
    pub avatar_url: Option<String>,
}

impl Default for PlexAuth {
    fn default() -> Self {
        Self::new().expect("Failed to create Plex authentication service")
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_plex_auth_creation() {
        let auth = PlexAuth::new();
        assert!(auth.is_ok());
    }

    #[test]
    fn test_auth_url_building() {
        let auth = PlexAuth::new().unwrap();
        let url = auth.build_auth_url("test_code").unwrap();
        
        assert!(url.contains("https://app.plex.tv/auth"));
        assert!(url.contains("clientID=discord-rich-presence-plex"));
        assert!(url.contains("code=test_code"));
        assert!(url.contains("context%5Bdevice%5D%5Bproduct%5D=Discord%20Rich%20Presence%20for%20Plex"));
    }

    // Note: Full authentication tests would require mocking the Plex API
    // or running integration tests with real Plex services
}
